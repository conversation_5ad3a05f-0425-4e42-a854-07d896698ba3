package router

import (
	"shop-order-backend/internal/config"
	"shop-order-backend/internal/controllers"
	"shop-order-backend/internal/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

// Setup 设置路由
func Setup(cfg *config.Config, db *xorm.Engine) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.GinMode)

	r := gin.Default()

	// CORS中间件
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = cfg.CORS.Origins
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"}
	r.Use(cors.New(corsConfig))

	// 初始化控制器
	authController := controllers.NewAuthController(db, cfg)
	customerController := controllers.NewCustomerController(db)
	addressController := controllers.NewAddressController(db)
	productController := controllers.NewProductController(db)
	orderController := controllers.NewOrderController(db)
	holidayController := controllers.NewHolidayController(db)
	lunarController := controllers.NewLunarController(db)
	uploadController := controllers.NewUploadController("./uploads", "http://localhost:8080")

	// API路由组
	api := r.Group("/api")
	{
		// 认证路由（无需JWT）
		auth := api.Group("/auth")
		{
			auth.POST("/login", authController.Login)
		}

		// 需要JWT认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware(cfg, db))
		{
			// 用户相关路由
			user := protected.Group("/user")
			{
				user.GET("/profile", authController.GetProfile)
				user.PUT("/password", authController.ChangePassword)
			}

			// 客户管理路由
			customers := protected.Group("/customers")
			{
				customers.GET("", customerController.GetCustomers)
				customers.POST("", customerController.CreateCustomer)
				customers.GET("/:id", customerController.GetCustomer)
				customers.PUT("/:id", customerController.UpdateCustomer)
				customers.DELETE("/:id", customerController.DeleteCustomer)

				// 客户地址路由 - 使用嵌套路由
				customers.GET("/:id/addresses", addressController.GetAddresses)
				customers.POST("/:id/addresses", addressController.CreateAddress)
				customers.PUT("/:id/addresses/:address_id/default", addressController.SetDefaultAddress)
			}

			// 地址管理路由
			addresses := protected.Group("/addresses")
			{
				addresses.PUT("/:id", addressController.UpdateAddress)
				addresses.DELETE("/:id", addressController.DeleteAddress)
			}

			// 商品管理路由
			products := protected.Group("/products")
			{
				products.GET("", productController.GetProducts)
				products.POST("", productController.CreateProduct)
				products.GET("/:id", productController.GetProduct)
				products.PUT("/:id", productController.UpdateProduct)
				products.DELETE("/:id", productController.DeleteProduct)
				products.GET("/categories", productController.GetCategories)

				// 库存管理路由
				products.PUT("/:id/stock", productController.UpdateStock)
				products.GET("/low-stock", productController.GetLowStockProducts)
			}

			// 订单管理路由
			orders := protected.Group("/orders")
			{
				orders.GET("", orderController.GetOrders)
				orders.POST("", orderController.CreateOrder)
				orders.GET("/:id", orderController.GetOrder)
				orders.PUT("/:id", orderController.UpdateOrder)
				orders.DELETE("/:id", orderController.DeleteOrder)

				// 订单统计路由
				orders.GET("/statistics", orderController.GetOrderStatistics)
				orders.GET("/summary/daily/:date", orderController.GetDailyOrderSummary)
				orders.GET("/summary/monthly", orderController.GetMonthlyOrderSummary)
				orders.GET("/trend/category", orderController.GetCategoryTrend)
			}

			// 节假日管理路由
			holidays := protected.Group("/holidays")
			{
				holidays.GET("", holidayController.GetHolidays)
				holidays.GET("/:date", holidayController.GetHolidayByDate)
				holidays.GET("/range", holidayController.GetHolidaysInRange)
				holidays.POST("", holidayController.CreateHoliday)
				holidays.PUT("/:id", holidayController.UpdateHoliday)
				holidays.DELETE("/:id", holidayController.DeleteHoliday)
				holidays.POST("/sync", holidayController.SyncHolidays)
			}

			// 农历日历路由
			calendar := protected.Group("/calendar")
			{
				calendar.GET("/lunar", lunarController.SolarToLunar)
				calendar.GET("/info", lunarController.GetCalendarInfo)
				calendar.GET("/month", lunarController.GetMonthCalendar)
				calendar.GET("/year", lunarController.GetYearCalendar)
			}

			// 文件上传路由
			upload := protected.Group("/upload")
			{
				upload.POST("/image", uploadController.UploadImage)
				upload.POST("/images", uploadController.UploadMultipleImages)
				upload.DELETE("/file/:filename", uploadController.DeleteFile)
				upload.GET("/file/:filename", uploadController.GetFileInfo)
			}

			// 管理员专用路由
			admin := protected.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				admin.POST("/users", authController.Register)
			}
		}
	}

	// 静态文件服务
	r.Static("/uploads", "./uploads")

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Shop Order Backend is running",
		})
	})

	return r
}

package main

import (
	"log"
	"os"

	"shop-order-backend/internal/config"
	"shop-order-backend/internal/database"
	"shop-order-backend/internal/router"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using system environment variables")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// 初始化定时任务服务
	// schedulerService := services.NewSchedulerService(db)
	// if err := schedulerService.Start(); err != nil {
	// 	log.Fatal("Failed to start scheduler service:", err)
	// }
	// defer schedulerService.Stop()

	// 初始化路由
	r := router.Setup(cfg, db)

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

<template>
  <el-container class="layout-container">
    <!-- 移动端遮罩层 -->
    <div
      class="mobile-overlay"
      :class="{ show: isMobileMenuOpen }"
      @click="closeMobileMenu"
    ></div>

    <!-- 侧边栏 -->
    <el-aside
      :width="sidebarWidth"
      class="sidebar"
      :class="{ 'mobile-open': isMobileMenuOpen }"
    >
      <div class="logo">
        <div class="logo-icon">
          <el-icon size="24"><Shop /></el-icon>
        </div>
        <transition name="fade">
          <div v-show="!isCollapsed" class="logo-text">
            <h3>小商店</h3>
            <span class="logo-subtitle">订单管理系统</span>
          </div>
        </transition>
      </div>

      <el-menu
        :default-active="$route.path"
        class="sidebar-menu"
        router
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        :collapse="isCollapsed"
        :collapse-transition="false"
      >
        <el-menu-item index="/">
          <el-icon><Odometer /></el-icon>
          <template #title>仪表盘</template>
        </el-menu-item>

        <el-menu-item index="/products">
          <el-icon><Goods /></el-icon>
          <template #title>商品管理</template>
        </el-menu-item>

        <el-menu-item index="/customers">
          <el-icon><User /></el-icon>
          <template #title>客户管理</template>
        </el-menu-item>

        <el-menu-item index="/orders">
          <el-icon><Document /></el-icon>
          <template #title>订单管理</template>
        </el-menu-item>

        <el-menu-item index="/calendar">
          <el-icon><Calendar /></el-icon>
          <template #title>生产日历</template>
        </el-menu-item>

        <el-menu-item v-if="isAdmin" index="/users">
          <el-icon><UserFilled /></el-icon>
          <template #title>用户管理</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon>
              <component :is="isCollapsed ? 'Expand' : 'Fold'" />
            </el-icon>
          </el-button>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-dropdown">
              <el-avatar :size="32" icon="UserFilled" />
              <span class="username">{{ authStore.user?.email }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <DemoModeAlert />
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Shop } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import DemoModeAlert from './DemoModeAlert.vue'

const router = useRouter()
const authStore = useAuthStore()

// 侧边栏折叠状态
const isCollapsed = ref(false)
// 移动端菜单状态
const isMobileMenuOpen = ref(false)
// 是否为移动端
const isMobile = ref(false)

const isAdmin = computed(() => authStore.isAdmin)

// 计算侧边栏宽度
const sidebarWidth = computed(() => {
  if (isMobile.value) {
    return '250px'
  }
  return isCollapsed.value ? '64px' : '250px'
})

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    isMobileMenuOpen.value = false
  }
}

const toggleSidebar = () => {
  if (isMobile.value) {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
  } else {
    isCollapsed.value = !isCollapsed.value
  }
}

const closeMobileMenu = () => {
  if (isMobile.value) {
    isMobileMenuOpen.value = false
  }
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能待实现')
      break
    case 'changePassword':
      ElMessage.info('修改密码功能待实现')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const result = await authStore.signOut()
        if (result.success) {
          router.push('/login')
        }
      } catch {
        // 用户取消退出
      }
      break
  }
}

// 初始化认证状态
onMounted(async () => {
  if (!authStore.initialized) {
    await authStore.initialize()
  }

  // 初始化移动端检查
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  background: linear-gradient(180deg, #304156 0%, #2c3e50 100%);
  overflow: hidden;
  transition: var(--transition-base);
  height: 100vh;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  box-shadow: var(--shadow-base);
  border-right: 1px solid var(--border-light);
}

.logo {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  margin-bottom: 0;
  overflow: hidden;
  padding: 0 20px;
  position: relative;
}

.logo::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-base);
  margin-right: 12px;
  flex-shrink: 0;
}

.logo-text {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.logo-text h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  white-space: nowrap;
  line-height: 1.2;
  color: white;
}

.logo-subtitle {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  margin-top: 2px;
  font-weight: 400;
}

.sidebar-menu {
  border: none;
  flex: 1;
  overflow-y: auto;
}

.header {
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  flex-shrink: 0;
  box-shadow: var(--shadow-light);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  font-size: 18px;
  color: #606266;
}

.header-right {
  display: flex;
  align-items: center;
  min-width: 0;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 12px;
  height: 40px;
  border-radius: 4px;
  transition: background-color 0.3s;
  min-width: 0;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.main-content {
  background-color: #f0f2f5;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;
  height: calc(100vh - 60px);
  min-width: 0;
}

/* 折叠状态下的菜单项样式优化 */
.sidebar-menu.el-menu--collapse .el-menu-item {
  padding: 0 20px !important;
}

.sidebar-menu.el-menu--collapse .el-menu-item .el-icon {
  margin-right: 0;
}

/* 折叠动画 */
.sidebar-menu {
  transition: width 0.3s ease;
}

/* Tooltip样式优化 */
.el-tooltip__popper {
  max-width: 200px;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .header {
    padding: 0 15px;
    position: relative;
    z-index: 999;
  }

  .username {
    display: none;
  }

  .main-content {
    height: calc(100vh - 60px);
    width: 100%;
    padding: 10px;
  }

  /* 移动端遮罩层 */
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
  }

  .mobile-overlay.show {
    display: block;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0 10px;
  }

  .main-content {
    padding: 5px;
  }

  .logo h3 {
    font-size: 14px;
  }
}
</style>

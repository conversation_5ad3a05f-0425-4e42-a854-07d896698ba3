import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { productAPI } from '../lib/api'

export const useProductsStore = defineStore('products', () => {
  // 状态
  const products = ref([])
  const loading = ref(false)
  const categories = ref([])

  // 分页相关状态
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const hasMore = ref(true)

  // 缓存相关状态
  const cache = ref(new Map())
  const cacheExpiry = ref(new Map())
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // 计算属性
  const listedProducts = computed(() => 
    products.value.filter(product => product.is_listed)
  )

  const productCategories = computed(() => {
    const cats = [...new Set(products.value.map(p => p.category).filter(Boolean))]
    return cats.sort()
  })

  // 缓存辅助方法
  const getCacheKey = (params = {}) => {
    return JSON.stringify({ ...params, page: currentPage.value, pageSize: pageSize.value })
  }

  const isValidCache = (key) => {
    const expiry = cacheExpiry.value.get(key)
    return expiry && Date.now() < expiry
  }

  const setCache = (key, data) => {
    cache.value.set(key, data)
    cacheExpiry.value.set(key, Date.now() + CACHE_DURATION)
  }

  const getCache = (key) => {
    if (isValidCache(key)) {
      return cache.value.get(key)
    }
    return null
  }

  const clearCache = () => {
    cache.value.clear()
    cacheExpiry.value.clear()
  }

  // 获取商品列表（支持分页和缓存）
  const fetchProducts = async (params = {}, useCache = true) => {
    try {
      loading.value = true

      // 构建缓存键
      const cacheKey = getCacheKey(params)

      // 检查缓存
      if (useCache) {
        const cachedData = getCache(cacheKey)
        if (cachedData) {
          products.value = cachedData.products
          total.value = cachedData.total
          hasMore.value = cachedData.hasMore
          return { success: true, data: cachedData.products, fromCache: true }
        }
      }

      // 构建查询参数
      const queryParams = {
        page: currentPage.value,
        limit: pageSize.value,
        ...params
      }

      const result = await productAPI.getProducts(queryParams)

      if (result.success) {
        const newProducts = result.data || []

        // 如果是第一页，替换数据；否则追加数据
        if (currentPage.value === 1) {
          products.value = newProducts
        } else {
          products.value = [...products.value, ...newProducts]
        }

        total.value = result.total || newProducts.length
        hasMore.value = newProducts.length === pageSize.value

        // 缓存数据
        if (useCache) {
          setCache(cacheKey, {
            products: products.value,
            total: total.value,
            hasMore: hasMore.value
          })
        }

        return { success: true, data: newProducts }
      } else {
        throw new Error(result.error || '获取商品列表失败')
      }

    } catch (error) {
      console.error('获取商品列表失败:', error)
      ElMessage.error('获取商品列表失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 加载更多商品
  const loadMoreProducts = async (params = {}) => {
    if (!hasMore.value || loading.value) {
      return { success: false, message: '没有更多数据或正在加载中' }
    }

    currentPage.value += 1
    return await fetchProducts(params, false) // 加载更多时不使用缓存
  }

  // 刷新商品列表
  const refreshProducts = async (params = {}) => {
    currentPage.value = 1
    clearCache()
    return await fetchProducts(params, false)
  }

  // 重置分页状态
  const resetPagination = () => {
    currentPage.value = 1
    hasMore.value = true
    total.value = 0
  }

  // 创建商品
  const createProduct = async (productData) => {
    try {
      loading.value = true

      const result = await productAPI.createProduct(productData)

      if (result.success) {
        products.value.unshift(result.data)
        total.value += 1
        clearCache() // 清除缓存以确保数据一致性
        ElMessage.success('商品创建成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error || '创建商品失败')
      }

    } catch (error) {
      console.error('创建商品失败:', error)
      ElMessage.error('创建商品失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新商品
  const updateProduct = async (id, updates) => {
    try {
      loading.value = true

      const result = await productAPI.updateProduct(id, updates)

      if (result.success) {
        const index = products.value.findIndex(p => p.id === id)
        if (index > -1) {
          products.value[index] = result.data
        }
        clearCache() // 清除缓存以确保数据一致性
        ElMessage.success('商品更新成功')
        return { success: true, data: result.data }
      } else {
        throw new Error(result.error || '更新商品失败')
      }

    } catch (error) {
      console.error('更新商品失败:', error)
      ElMessage.error('更新商品失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 删除商品
  const deleteProduct = async (id) => {
    try {
      loading.value = true

      const result = await productAPI.deleteProduct(id)

      if (result.success) {
        const index = products.value.findIndex(p => p.id === id)
        if (index > -1) {
          products.value.splice(index, 1)
          total.value -= 1
        }
        clearCache() // 清除缓存以确保数据一致性
        ElMessage.success('商品删除成功')
        return { success: true }
      } else {
        throw new Error(result.error || '删除商品失败')
      }

    } catch (error) {
      console.error('删除商品失败:', error)
      ElMessage.error('删除商品失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 上传商品图片
  const uploadProductImage = async (file, productId) => {
    try {
      const formData = new FormData()
      formData.append('image', file)
      formData.append('product_id', productId)

      const result = await productAPI.uploadImage(formData)

      if (result.success) {
        return { success: true, url: result.url }
      } else {
        throw new Error(result.error || '上传图片失败')
      }

    } catch (error) {
      console.error('上传图片失败:', error)
      ElMessage.error('上传图片失败')
      return { success: false, error }
    }
  }

  // 切换商品上架状态
  const toggleProductStatus = async (id, isListed) => {
    return await updateProduct(id, { is_listed: isListed })
  }

  // 根据ID获取商品
  const getProductById = (id) => {
    return products.value.find(p => p.id === id)
  }

  // 搜索商品
  const searchProducts = (query, filters = {}) => {
    let filtered = products.value

    // 文本搜索
    if (query) {
      const searchTerm = query.toLowerCase()
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        (product.category && product.category.toLowerCase().includes(searchTerm))
      )
    }

    // 类别筛选
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category)
    }

    // 状态筛选
    if (filters.status !== undefined) {
      filtered = filtered.filter(product => product.is_listed === filters.status)
    }

    return filtered
  }

  return {
    // 状态
    products,
    loading,
    categories,
    currentPage,
    pageSize,
    total,
    hasMore,

    // 计算属性
    listedProducts,
    productCategories,

    // 方法
    fetchProducts,
    loadMoreProducts,
    refreshProducts,
    resetPagination,
    createProduct,
    updateProduct,
    deleteProduct,
    uploadProductImage,
    toggleProductStatus,
    getProductById,
    searchProducts,
    clearCache
  }
})

// 农历计算工具函数
import { Lunar, Solar } from 'lunar-javascript'

/**
 * 公历转农历
 * @param {Date|string} date - 公历日期
 * @returns {Object} 农历信息
 */
export function solarToLunar(date) {
  try {
    const solar = typeof date === 'string' ? Solar.fromYmd(
      parseInt(date.split('-')[0]),
      parseInt(date.split('-')[1]),
      parseInt(date.split('-')[2])
    ) : Solar.fromDate(date)
    
    const lunar = solar.getLunar()
    
    return {
      year: lunar.getYear(),
      month: lunar.getMonth(),
      day: lunar.getDay(),
      yearCn: lunar.getYearInChinese(),
      monthCn: lunar.getMonthInChinese(),
      dayCn: lunar.getDayInChinese(),
      yearGanZhi: lunar.getYearInGanZhi(),
      monthGanZhi: lunar.getMonthInGanZhi(),
      dayGanZhi: lunar.getDayInGan<PERSON><PERSON>(),
      timeGanZhi: lunar.getTimeInGan<PERSON><PERSON>(),
      zodiac: lunar.getYearShengXiao(),
      festivals: lunar.getFestivals(),
      jieQi: lunar.getJieQi(),
      isLeapMonth: lunar.isLeap(),
      weekDay: solar.getWeek(),
      weekDayCn: solar.getWeekInChinese(),
      constellation: solar.getXingZuo(),
      lunarDateStr: `${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`,
      fullLunarStr: `${lunar.getYearInChinese()}年${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`
    }
  } catch (error) {
    console.error('农历转换失败:', error)
    return {
      year: 0,
      month: 0,
      day: 0,
      yearCn: '',
      monthCn: '',
      dayCn: '',
      yearGanZhi: '',
      monthGanZhi: '',
      dayGanZhi: '',
      timeGanZhi: '',
      zodiac: '',
      festivals: [],
      jieQi: '',
      isLeapMonth: false,
      weekDay: 0,
      weekDayCn: '',
      constellation: '',
      lunarDateStr: '',
      fullLunarStr: ''
    }
  }
}

/**
 * 农历转公历
 * @param {number} year - 农历年
 * @param {number} month - 农历月
 * @param {number} day - 农历日
 * @param {boolean} isLeapMonth - 是否闰月
 * @returns {Object} 公历信息
 */
export function lunarToSolar(year, month, day, isLeapMonth = false) {
  try {
    const lunar = Lunar.fromYmd(year, month, day, isLeapMonth)
    const solar = lunar.getSolar()
    
    return {
      year: solar.getYear(),
      month: solar.getMonth(),
      day: solar.getDay(),
      date: new Date(solar.getYear(), solar.getMonth() - 1, solar.getDay()),
      dateStr: `${solar.getYear()}-${solar.getMonth().toString().padStart(2, '0')}-${solar.getDay().toString().padStart(2, '0')}`,
      weekDay: solar.getWeek(),
      weekDayCn: solar.getWeekInChinese(),
      constellation: solar.getXingZuo()
    }
  } catch (error) {
    console.error('公历转换失败:', error)
    return {
      year: 0,
      month: 0,
      day: 0,
      date: null,
      dateStr: '',
      weekDay: 0,
      weekDayCn: '',
      constellation: ''
    }
  }
}

/**
 * 获取指定月份的农历信息
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {Array} 月份农历信息数组
 */
export function getMonthLunarInfo(year, month) {
  const monthInfo = []
  const daysInMonth = new Date(year, month, 0).getDate()
  
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day)
    const lunarInfo = solarToLunar(date)
    monthInfo.push({
      solarDate: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
      solarDay: day,
      ...lunarInfo
    })
  }
  
  return monthInfo
}

/**
 * 获取农历节日
 * @param {Date|string} date - 日期
 * @returns {Array} 节日数组
 */
export function getLunarFestivals(date) {
  const lunarInfo = solarToLunar(date)
  return lunarInfo.festivals || []
}

/**
 * 获取节气信息
 * @param {Date|string} date - 日期
 * @returns {string} 节气名称
 */
export function getJieQi(date) {
  const lunarInfo = solarToLunar(date)
  return lunarInfo.jieQi || ''
}

/**
 * 判断是否为农历节日
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否为节日
 */
export function isLunarFestival(date) {
  const festivals = getLunarFestivals(date)
  return festivals.length > 0
}

/**
 * 获取生肖
 * @param {number} year - 年份
 * @returns {string} 生肖
 */
export function getZodiac(year) {
  try {
    const lunar = Lunar.fromDate(new Date(year, 0, 1))
    return lunar.getYearShengXiao()
  } catch (error) {
    console.error('获取生肖失败:', error)
    return ''
  }
}

/**
 * 获取星座
 * @param {Date|string} date - 日期
 * @returns {string} 星座
 */
export function getConstellation(date) {
  try {
    const solar = typeof date === 'string' ? Solar.fromYmd(
      parseInt(date.split('-')[0]),
      parseInt(date.split('-')[1]),
      parseInt(date.split('-')[2])
    ) : Solar.fromDate(date)
    
    return solar.getXingZuo()
  } catch (error) {
    console.error('获取星座失败:', error)
    return ''
  }
}

/**
 * 格式化农历日期显示
 * @param {Object} lunarInfo - 农历信息
 * @returns {string} 格式化的农历日期
 */
export function formatLunarDate(lunarInfo) {
  if (!lunarInfo || !lunarInfo.monthCn || !lunarInfo.dayCn) {
    return ''
  }
  
  // 如果是初一，显示月份
  if (lunarInfo.dayCn === '初一') {
    return lunarInfo.monthCn
  }
  
  // 如果有节气，优先显示节气
  if (lunarInfo.jieQi) {
    return lunarInfo.jieQi
  }
  
  // 如果有节日，优先显示节日
  if (lunarInfo.festivals && lunarInfo.festivals.length > 0) {
    return lunarInfo.festivals[0]
  }
  
  // 否则显示农历日期
  return lunarInfo.dayCn
}

/**
 * 获取农历月份天数
 * @param {number} year - 农历年
 * @param {number} month - 农历月
 * @returns {number} 天数
 */
export function getLunarMonthDays(year, month) {
  try {
    const lunar = Lunar.fromYmd(year, month, 1)
    return lunar.getDayCount()
  } catch (error) {
    console.error('获取农历月份天数失败:', error)
    return 30
  }
}

/**
 * 判断是否为闰月
 * @param {number} year - 农历年
 * @param {number} month - 农历月
 * @returns {boolean} 是否为闰月
 */
export function isLeapMonth(year, month) {
  try {
    const lunar = Lunar.fromYmd(year, month, 1)
    return lunar.isLeap()
  } catch (error) {
    console.error('判断闰月失败:', error)
    return false
  }
}

// 导出所有函数
export default {
  solarToLunar,
  lunarToSolar,
  getMonthLunarInfo,
  getLunarFestivals,
  getJieQi,
  isLunarFestival,
  getZodiac,
  getConstellation,
  formatLunarDate,
  getLunarMonthDays,
  isLeapMonth
}
